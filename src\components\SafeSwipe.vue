<template>
  <!-- 只有在容器完全准备好时才渲染轮播组件 -->
  <van-swipe
    v-if="isReady"
    ref="swipe"
    v-bind="$attrs"
    v-on="$listeners"
    @error="handleError"
  >
    <slot></slot>
  </van-swipe>
  <!-- 加载占位符 -->
  <div v-else class="safe-swipe-placeholder">
    <div class="loading-text">{{ loadingText }}</div>
  </div>
</template>

<script>
import { Swipe } from 'vant';

export default {
  name: 'SafeSwipe',
  components: {
    'van-swipe': Swipe
  },
  props: {
    loadingText: {
      type: String,
      default: '加载中...'
    },
    // 延迟渲染时间（毫秒）
    delay: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      isReady: false,
      errorCount: 0,
      maxErrors: 3
    };
  },
  mounted() {
    this.initSafeSwipe();
  },
  methods: {
    async initSafeSwipe() {
      try {
        // 等待DOM准备就绪
        await this.waitForContainer();
        
        // 延迟一小段时间确保所有样式都已应用
        await this.delay > 0 ? new Promise(resolve => setTimeout(resolve, this.delay)) : Promise.resolve();
        
        // 最终检查容器是否有有效尺寸
        if (this.hasValidSize()) {
          this.isReady = true;
          console.log('SafeSwipe: 轮播组件已安全初始化');
          
          // 通知父组件初始化完成
          this.$emit('ready');
          
          // 在下一个tick中调用resize确保正确初始化
          this.$nextTick(() => {
            if (this.$refs.swipe && this.$refs.swipe.resize) {
              setTimeout(() => {
                try {
                  this.$refs.swipe.resize();
                } catch (error) {
                  console.log('SafeSwipe: resize调用失败，但组件已正常初始化');
                }
              }, 50);
            }
          });
        } else {
          throw new Error('容器尺寸无效');
        }
      } catch (error) {
        console.error('SafeSwipe: 初始化失败', error);
        this.handleInitError();
      }
    },
    
    waitForContainer() {
      return new Promise((resolve, reject) => {
        const startTime = Date.now();
        const timeout = 5000; // 5秒超时
        
        const checkContainer = () => {
          if (this.hasValidSize()) {
            resolve();
            return;
          }
          
          if (Date.now() - startTime > timeout) {
            reject(new Error('等待容器超时'));
            return;
          }
          
          setTimeout(checkContainer, 50);
        };
        
        this.$nextTick(() => {
          checkContainer();
        });
      });
    },
    
    hasValidSize() {
      const el = this.$el;
      return el && el.offsetWidth > 0 && el.offsetHeight > 0;
    },
    
    handleError(error) {
      console.error('SafeSwipe: 轮播组件错误', error);
      this.errorCount++;
      
      if (this.errorCount < this.maxErrors) {
        console.log(`SafeSwipe: 尝试重新初始化 (${this.errorCount}/${this.maxErrors})`);
        this.reinitialize();
      } else {
        console.log('SafeSwipe: 错误次数过多，停止重试');
        this.$emit('error', error);
      }
    },
    
    handleInitError() {
      // 即使初始化失败，也要显示组件避免无限等待
      setTimeout(() => {
        if (!this.isReady) {
          console.log('SafeSwipe: 强制设置为准备状态');
          this.isReady = true;
          this.$emit('ready');
        }
      }, 2000);
    },
    
    reinitialize() {
      this.isReady = false;
      setTimeout(() => {
        this.initSafeSwipe();
      }, 500);
    },
    
    // 暴露给父组件的方法
    resize() {
      if (this.$refs.swipe && this.$refs.swipe.resize) {
        try {
          this.$refs.swipe.resize();
        } catch (error) {
          console.log('SafeSwipe: resize方法调用失败', error);
        }
      }
    },
    
    swipeTo(index, options) {
      if (this.$refs.swipe && this.$refs.swipe.swipeTo) {
        try {
          this.$refs.swipe.swipeTo(index, options);
        } catch (error) {
          console.log('SafeSwipe: swipeTo方法调用失败', error);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.safe-swipe-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  
  .loading-text {
    color: #999;
    font-size: 14px;
  }
}
</style>
