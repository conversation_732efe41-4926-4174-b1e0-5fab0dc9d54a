/**
 * 轮播组件错误处理工具
 * 用于统一处理轮播组件的各种错误情况
 */

export class SwipeErrorHandler {
  constructor() {
    this.errorCount = 0;
    this.maxRetries = 3;
    this.lastErrorTime = 0;
    this.debounceTime = 2000; // 2秒防抖
  }

  /**
   * 检查是否应该处理错误（防抖机制）
   */
  shouldHandleError() {
    const now = Date.now();
    if (now - this.lastErrorTime < this.debounceTime) {
      console.log('轮播错误处理防抖：忽略频繁错误');
      return false;
    }
    
    if (this.errorCount >= this.maxRetries) {
      console.log('轮播错误重试次数已达上限');
      return false;
    }
    
    this.lastErrorTime = now;
    this.errorCount++;
    return true;
  }

  /**
   * 重置错误计数
   */
  resetErrorCount() {
    this.errorCount = 0;
  }

  /**
   * 检查是否是轮播相关错误
   */
  isSwipeError(error) {
    if (!error || !error.message) return false;
    
    const errorMessage = error.message.toLowerCase();
    const swipeErrorKeywords = [
      'cannot read properties of null',
      'cannot read properties of undefined',
      'width',
      'offsetwidth',
      'minoffset',
      'swipe',
      'move'
    ];
    
    return swipeErrorKeywords.some(keyword => errorMessage.includes(keyword));
  }

  /**
   * 安全地等待DOM元素准备就绪
   */
  waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(selector);
        
        if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
          resolve(element);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`等待元素 ${selector} 超时`));
          return;
        }
        
        setTimeout(checkElement, 50);
      };
      
      checkElement();
    });
  }

  /**
   * 安全地初始化轮播组件
   */
  async safeInitSwipe(vueInstance, containerSelector = '.banner-section') {
    try {
      console.log('开始安全初始化轮播组件');
      
      // 等待容器准备就绪
      await this.waitForElement(containerSelector);
      console.log('轮播容器已准备就绪');
      
      // 等待Vue的下一个tick
      await vueInstance.$nextTick();

      // 设置容器和轮播组件为准备状态
      vueInstance.containerReady = true;
      vueInstance.bannerSwipeReady = true;

      // 再次等待DOM更新
      await vueInstance.$nextTick();
      
      // 如果有轮播组件引用，尝试调用resize方法
      if (vueInstance.$refs.bannerSwipe && vueInstance.$refs.bannerSwipe.resize) {
        setTimeout(() => {
          try {
            vueInstance.$refs.bannerSwipe.resize();
            console.log('轮播组件resize成功');
            this.resetErrorCount(); // 成功后重置错误计数
          } catch (error) {
            console.log('轮播组件resize失败:', error);
          }
        }, 100);
      }
      
      console.log('轮播组件安全初始化完成');
      
    } catch (error) {
      console.error('轮播组件安全初始化失败:', error);
      // 即使失败也要设置为准备状态，避免无限等待
      vueInstance.containerReady = true;
      vueInstance.bannerSwipeReady = true;
    }
  }
}

// 创建单例实例
export const swipeErrorHandler = new SwipeErrorHandler();

// 默认导出
export default swipeErrorHandler;
