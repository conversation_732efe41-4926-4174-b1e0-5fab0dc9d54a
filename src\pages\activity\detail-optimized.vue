<!-- 优化后的活动详情页面 - 滚动吸顶功能修复版本 -->
<template>
  <div class="activity-detail">
    <!-- 导航栏 -->
    <van-nav-bar
      title="活动详情"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 活动内容 -->
    <div v-if="activity.id" class="activity-content">
      <!-- 活动头图轮播 -->
      <div class="activity-header">
        <div class="header-image-container">
          <template v-if="activity.images && activity.images.length > 1">
            <van-swipe class="header-swipe" :autoplay="3000" indicator-color="white">
              <van-swipe-item v-for="(image, index) in activity.images" :key="index">
                <img :src="image" :alt="`活动图片${index + 1}`" class="header-image" />
              </van-swipe-item>
            </van-swipe>
          </template>
          <template v-else>
            <img 
              :src="activity.headerImg || activity.images?.[0] || require('@/images/img/background.png')" 
              alt="活动头图" 
              class="header-image" 
            />
          </template>
          
          <!-- 活动状态标签 -->
          <div class="activity-status-tag" :class="activity.statusClass">
            {{ activity.statusText }}
          </div>
        </div>
      </div>

      <!-- 活动信息卡片 -->
      <div class="activity-info-card">
        <h2 class="activity-name">{{ activity.title }}</h2>
        
        <div class="activity-time">
          <img src="@/images/icon/time.png" alt="时间" class="info-icon" />
          <span class="info-text">{{ activity.timeText || '时间待定' }}</span>
        </div>
        
        <div class="activity-location">
          <img src="@/images/icon/location.png" alt="地点" class="info-icon" />
          <span class="info-text">{{ activity.location || '地点待定' }}</span>
        </div>
      </div>

      <!-- 活动详情和须知标签页 -->
      <div class="activity-tabs-section">
        <!-- 吸顶标签页 -->
        <div 
          class="tabs-header" 
          :class="{ 'sticky': isTabsSticky }" 
          ref="tabsHeader" 
          :style="{ backgroundImage: `url(${getTabBackgroundImage()})` }"
        >
          <div class="tabs-container">
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'description' }"
              @click="switchTab('description')"
            >
              活动详情介绍
            </div>
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'notice' }"
              @click="switchTab('notice')"
            >
              活动报名须知
            </div>
          </div>
        </div>
        
        <!-- 占位元素，防止吸顶时布局跳动 -->
        <div class="tabs-header-placeholder" v-show="isTabsSticky"></div>
        
        <!-- 标签页内容 -->
        <div class="tabs-content">
          <!-- 活动详情内容 -->
          <div class="tab-panel" v-show="activeTab === 'description'">
            <div class="description-content" v-html="activity.description"></div>
          </div>
          
          <!-- 活动须知内容 -->
          <div class="tab-panel" v-show="activeTab === 'notice'">
            <div class="notice-content" v-html="getNoticeContent()"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-container" type="spinner" size="24px">
      加载中...
    </van-loading>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="activity.id && shouldShowButton">
      <van-button
        class="register-button"
        :class="getButtonClass()"
        size="large"
        :disabled="isButtonDisabled"
        @click="handleRegister"
      >
        {{ getButtonText() }}
      </van-button>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Loading,
  Swipe,
  SwipeItem
} from 'vant';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Loading)
  .use(Swipe)
  .use(SwipeItem);

export default {
  name: 'ActivityDetail',
  data() {
    return {
      activity: {},
      activeTab: 'description',
      isTabsSticky: false,
      scrollHandler: null,
      intersectionObserver: null
    };
  },
  
  computed: {
    shouldShowButton() {
      if (!this.activity.id) return false;
      const isRegistrationOngoing = this.activity.registrationStatus === 'ongoing' || 
                                   this.activity.activityStatus === 'ongoing';
      const isUserRegistered = this.activity.userRegistered === true;
      return isRegistrationOngoing && !isUserRegistered;
    },

    isButtonDisabled() {
      return false;
    }
  },

  mounted() {
    this.loadActivityDetail();
    this.$nextTick(() => {
      // 延迟绑定，确保DOM完全渲染
      setTimeout(() => {
        this.initStickyBehavior();
      }, 300);
    });
  },
  
  beforeDestroy() {
    this.cleanupStickyBehavior();
  },

  methods: {
    async loadActivityDetail() {
      try {
        const activityId = this.$route.params.id;
        const response = await this.$api.getActivityDetail(activityId);
        
        if (response && response.success === 1) {
          this.activity = {
            ...response.value,
            statusText: this.getActivityStatusText(response.value.activityStatus),
            statusClass: this.getStatusClass(response.value.activityStatus),
            timeText: response.value.actTime || '时间待定'
          };
        }
      } catch (error) {
        console.error('加载活动详情失败:', error);
      }
    },

    // 初始化吸顶行为 - 多重保障方案
    initStickyBehavior() {
      if (!this.$refs.tabsHeader) {
        console.warn('标签页头部元素未找到，延迟重试');
        setTimeout(() => this.initStickyBehavior(), 200);
        return;
      }

      console.log('初始化吸顶功能');
      
      // 方案1: 使用 Intersection Observer (现代浏览器推荐)
      this.setupIntersectionObserver();
      
      // 方案2: 传统滚动事件监听 (兼容性保障)
      this.setupScrollListener();
      
      // 方案3: 定时检查 (最后保障)
      this.setupPeriodicCheck();
    },

    // 方案1: Intersection Observer
    setupIntersectionObserver() {
      if (!window.IntersectionObserver) return;

      this.intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          const shouldBeSticky = entry.boundingClientRect.top <= 0 && !entry.isIntersecting;
          this.updateStickyState(shouldBeSticky);
        });
      }, {
        root: null,
        rootMargin: '0px',
        threshold: [0, 1]
      });

      this.intersectionObserver.observe(this.$refs.tabsHeader);
    },

    // 方案2: 滚动事件监听
    setupScrollListener() {
      this.scrollHandler = this.throttle(() => {
        if (!this.$refs.tabsHeader) return;
        
        const rect = this.$refs.tabsHeader.getBoundingClientRect();
        const shouldBeSticky = rect.top <= 0;
        this.updateStickyState(shouldBeSticky);
      }, 16); // 约60fps

      // 绑定到多个可能的滚动容器
      window.addEventListener('scroll', this.scrollHandler, { passive: true });
      document.addEventListener('scroll', this.scrollHandler, { passive: true });
    },

    // 方案3: 定时检查
    setupPeriodicCheck() {
      this.periodicTimer = setInterval(() => {
        if (!this.$refs.tabsHeader) return;
        
        const rect = this.$refs.tabsHeader.getBoundingClientRect();
        const shouldBeSticky = rect.top <= 0;
        this.updateStickyState(shouldBeSticky);
      }, 100);
    },

    // 更新吸顶状态
    updateStickyState(shouldBeSticky) {
      if (this.isTabsSticky !== shouldBeSticky) {
        this.isTabsSticky = shouldBeSticky;
      }
    },

    // 清理所有监听器
    cleanupStickyBehavior() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }
      
      if (this.scrollHandler) {
        window.removeEventListener('scroll', this.scrollHandler);
        document.removeEventListener('scroll', this.scrollHandler);
        this.scrollHandler = null;
      }
      
      if (this.periodicTimer) {
        clearInterval(this.periodicTimer);
        this.periodicTimer = null;
      }
    },

    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    },

    switchTab(tab) {
      this.activeTab = tab;
    },

    getTabBackgroundImage() {
      return require('@/images/img/tab-bg.png');
    },

    getNoticeContent() {
      return `
        <p><strong>活动报名须知</strong></p>
        <p>1. 请确保提供的个人信息真实有效</p>
        <p>2. 活动当天请携带有效身份证件</p>
        <p>3. 如需取消报名，请提前24小时联系客服</p>
        <p>4. 活动期间请遵守相关规定和安排</p>
      `;
    },

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束',
        '0': '未开始',
        '1': '进行中',
        '2': '已结束'
      };
      return statusMap[status] || '未知状态';
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'status-not-started',
        'ongoing': 'status-ongoing',
        'ended': 'status-ended',
        '0': 'status-not-started',
        '1': 'status-ongoing',
        '2': 'status-ended'
      };
      return statusMap[status] || 'status-not-started';
    },

    getButtonClass() {
      return this.activity.userRegistered ? 'registered' : 'primary';
    },

    getButtonText() {
      return this.activity.userRegistered ? '已报名' : '一键报名';
    },

    handleRegister() {
      if (this.activity.userRegistered) {
        this.$router.push(`/registration-detail/${this.activity.id}`);
        return;
      }

      const isRegistrationOngoing = this.activity.registrationStatus === 'ongoing' || 
                                   this.activity.activityStatus === 'ongoing';
      
      if (!isRegistrationOngoing) {
        this.$toast('当前活动报名未开始或已结束');
        return;
      }

      // 这里添加报名逻辑
      console.log('开始报名流程');
    }
  }
};
</script>

<style lang="less" scoped>
.activity-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

.activity-content {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.activity-header {
  position: relative;

  .header-image-container {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }

  .header-swipe {
    height: 100%;

    :deep(.van-swipe__indicators) {
      bottom: 20px;
    }
  }

  .header-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .activity-status-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);

    &.status-not-started {
      background: rgba(102, 102, 102, 0.8);
    }

    &.status-ongoing {
      background: rgba(82, 196, 26, 0.8);
    }

    &.status-ended {
      background: rgba(250, 140, 22, 0.8);
    }
  }
}

.activity-info-card {
  width: 100%;
  min-height: 185px;
  margin: 0;
  border-radius: 16.5px;
  background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: -13px;
  position: relative;
  z-index: 1;

  .activity-name {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    font-size: 18px;
    line-height: 25px;
    color: #292929;
    text-align: left;
    letter-spacing: 0;
    word-break: break-all;
    margin-bottom: 16px;
    flex-shrink: 0;
    padding: 20px 20px 0 20px;
  }

  .activity-time,
  .activity-location {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    flex-shrink: 0;
    padding: 0 20px;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 20px;
    }

    .info-icon {
      width: 12px;
      height: 12px;
      margin-right: 6px;
      flex-shrink: 0;
      object-fit: contain;
    }

    .info-text {
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-weight: 400;
      font-size: 11px;
      line-height: 15px;
      color: #AAAAAA;
      text-align: left;
      letter-spacing: 0;
      flex: 1;
    }
  }
}

.activity-tabs-section {
  margin-top: -65px;

  .tabs-header {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    border-bottom: none;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    min-height: 80px;
    display: flex;
    align-items: center;

    &.sticky {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1000 !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transform: translateZ(0);
    }

    .tabs-container {
      display: flex;
      padding-left: 40px;
      padding-right: 45px;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: space-between;

      .tab-item {
        flex: none;
        padding: 0;
        text-align: center;
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 500;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        &.active {
          color: #292929;
        }

        &:not(.active) {
          color: #FFFFFF;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .tabs-header-placeholder {
    height: 80px;
    width: 100%;
  }

  .tabs-content {
    .tab-panel {
      padding: 20px 16px;
      border-bottom: 8px solid #f8f8f8;

      .description-content,
      .notice-content {
        font-size: 14px;
        line-height: 1.6;
        color: #333;

        :deep(p) {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #eee;
  z-index: 100;

  .register-button {
    width: 100%;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;

    &.primary {
      background: linear-gradient(135deg, #ff6b6b, #ee5a24);
      border: none;
      color: white;
    }

    &.registered {
      background: #f0f0f0;
      color: #999;
      border: 1px solid #ddd;
    }
  }
}
</style>
