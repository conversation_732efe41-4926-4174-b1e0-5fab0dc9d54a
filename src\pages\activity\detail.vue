<template>
  <div class="activity-detail">
    <!-- 活动内容 -->
    <div class="detail-content" v-if="activity.id">
      <!-- 活动头图 -->
      <div class="activity-header">
        <div class="header-image-container" v-if="activity.images && activity.images.length > 0">
          <van-swipe 
            class="header-swipe" 
            :autoplay="3000" 
            :show-indicators="activity.images.length > 1"
            indicator-color="rgba(255,255,255,0.5)"
          >
            <van-swipe-item v-for="(image, index) in activity.images" :key="index">
              <img :src="image" :alt="activity.title" class="header-image" />
            </van-swipe-item>
          </van-swipe>
        </div>
        <div class="header-image-container" v-else>
          <img :src="activity.imageUrl" :alt="activity.title" class="header-image" />
        </div>
        
        <!-- 活动状态标签 -->
        <div class="activity-status-tag" :class="getStatusClass(activity.activityStatus)">
          {{ getActivityStatusText(activity.activityStatus) }}
        </div>
      </div>


      <!-- 活动信息卡片 -->
      <div class="activity-info-card">
        <!-- 活动名称 -->
        <div class="activity-name">{{ activity.title }}</div>
        
        <!-- 活动时间 -->
        <div class="activity-time">
          <img src="@/assets/images/detailTime.png" alt="时间" class="info-icon" />
          <span class="info-text">{{ formatActivityTime(activity.activityTime) }}</span>
        </div>
        
        <!-- 活动地址 -->
        <div class="activity-location" v-if="activity.location">
          <img src="@/assets/images/detailLocation.png" alt="地址" class="info-icon" />
          <span class="info-text">{{ activity.location }}</span>
        </div>
      </div>

      <!-- 报名人数 -->
      <!-- <div class="participant-section">
        <span class="participant-text">报名人数：{{ activity.participantCount }}/{{ activity.maxParticipants }} 人</span>
      </div> -->

      <!-- 活动详情和须知标签页 -->
      <div class="activity-tabs-section">
        <!-- 调试信息和测试按钮 (开发时使用，生产环境可删除) -->
        <div v-if="$options.name === 'ActivityDetail'" style="position: fixed; top: 50px; right: 10px; z-index: 9999; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px;">
          <div>吸顶状态: {{ isTabsSticky ? '是' : '否' }}</div>
          <div>滚动位置: {{ scrollPosition }}</div>
          <button @click="testStickyToggle" style="margin-top: 5px; padding: 5px; background: #007bff; color: white; border: none; border-radius: 3px;">
            测试切换吸顶
          </button>
          <button @click="forceCheckScroll" style="margin-top: 5px; padding: 5px; background: #28a745; color: white; border: none; border-radius: 3px;">
            强制检查滚动
          </button>
        </div>

        <!-- 吸顶标签页 -->
        <div class="tabs-header" :class="{ 'sticky': isTabsSticky }" ref="tabsHeader" :style="getStickyHeaderStyle()">
          <div class="tabs-container">
            <div
              class="tab-item"
              :class="{ active: activeTab === 'description' }"
              @click="switchTab('description')"
            >
              活动详情介绍
            </div>
            <div
              class="tab-item"
              :class="{ active: activeTab === 'notice' }"
              @click="switchTab('notice')"
            >
              活动报名须知
            </div>
          </div>
        </div>
        
        <!-- 占位元素，防止吸顶时布局跳动 -->
        <div class="tabs-header-placeholder" v-show="isTabsSticky" :style="{ height: '80px' }"></div>

        <!-- 调试面板 -->
        <div style="position: fixed; top: 100px; right: 10px; background: rgba(0,0,0,0.9); color: white; padding: 10px; border-radius: 5px; font-size: 11px; z-index: 9999; max-width: 200px;">
          <div><strong>调试信息</strong></div>
          <div>吸顶状态: <span :style="{ color: isTabsSticky ? '#4CAF50' : '#f44336' }">{{ isTabsSticky ? '是' : '否' }}</span></div>
          <div>滚动位置: {{ scrollPosition }}</div>
          <div>元素top: {{ elementTop.toFixed(2) }}</div>
          <div>当前标签: {{ activeTab }}</div>
          <div>时间: {{ new Date().toLocaleTimeString() }}</div>
          <button @click="toggleSticky" style="margin-top: 5px; padding: 5px; width: 100%;">手动切换吸顶</button>
          <button @click="forceCheck" style="margin-top: 2px; padding: 5px; width: 100%;">强制检查</button>
        </div>

        <!-- 标签页内容 -->
        <div class="tabs-content">
          <!-- 活动详情内容 -->
          <div class="tab-panel" v-show="activeTab === 'description'">
            <div class="description-content" v-html="activity.description"></div>
          </div>
          
          <!-- 活动须知内容 -->
          <div class="tab-panel" v-show="activeTab === 'notice'">
            <div class="notice-content" v-html="getNoticeContent()"></div>
          </div>
        </div>
      </div>

      <!-- 报名信息 -->
      <div class="registration-info" v-if="activity.registrationInfo">
        <h3>报名信息</h3>
        <div class="registration-content">
          <div class="info-item" v-for="(item, index) in activity.registrationInfo" :key="index">
            <span class="label">{{ item.label }}：</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="contact-info" v-if="activity.contactInfo">
        <h3>联系方式</h3>
        <div class="contact-content">
          <div class="contact-item" v-for="(item, index) in activity.contactInfo" :key="index">
            <span class="label">{{ item.label }}：</span>
            <span class="value" @click="handleContact(item)">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-container" type="spinner" size="24px">
      加载中...
    </van-loading>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="activity.id && shouldShowButton">
      <van-button
        class="register-button"
        :class="getButtonClass()"
        size="large"
        :disabled="isButtonDisabled"
        @click="handleRegister"
      >
        {{ getButtonText() }}
      </van-button>
    </div>

    <!-- 报名弹窗 -->
    <van-popup v-model="showRegisterPopup" position="bottom" :style="{ height: '60%' }">
      <div class="register-popup">
        <div class="popup-header">
          <h3>活动报名</h3>
          <van-icon name="cross" @click="showRegisterPopup = false" />
        </div>
        
        <div class="register-form">
          <van-field
            v-model="registerForm.name"
            label="姓名"
            placeholder="请输入您的姓名"
            required
          />
          <van-field
            v-model="registerForm.phone"
            label="手机号"
            placeholder="请输入您的手机号"
            type="tel"
            required
          />
          <van-field
            v-model="registerForm.cardType"
            label="证件类型"
            placeholder="请选择证件类型"
          />
          <van-field
            v-model="registerForm.idCard"
            label="身份证号"
            placeholder="请输入身份证号"
          />
          <van-field
            v-model="registerForm.gender"
            label="性别"
            placeholder="请选择性别"
          />
          <van-field
            v-model="registerForm.age"
            label="年龄"
            placeholder="请输入年龄"
            type="number"
          />
          <van-field
            v-model="registerForm.human"
            label="携带成人数"
            placeholder="请输入携带成人数"
            type="number"
          />
          <van-field
            v-model="registerForm.child"
            label="携带儿童数"
            placeholder="请输入携带儿童数"
            type="number"
          />
          <van-field
            v-model="registerForm.high"
            label="身高(cm)"
            placeholder="请输入身高"
            type="number"
          />
          <van-field
            v-model="registerForm.weight"
            label="体重(kg)"
            placeholder="请输入体重"
            type="number"
          />
          <van-field
            v-model="registerForm.educate"
            label="学历"
            placeholder="请输入学历"
          />
          <van-field
            v-model="registerForm.community"
            label="社区"
            placeholder="请输入所在社区"
          />
          <van-field
            v-model="registerForm.address"
            label="地址"
            placeholder="请输入详细地址"
            type="textarea"
            rows="2"
          />
        </div>

        <div class="popup-footer">
          <van-button type="default" @click="showRegisterPopup = false">取消</van-button>
          <van-button type="primary" @click="submitRegister" :loading="submitting">确认报名</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Loading,
  Popup,
  Field,
  Toast,
  Dialog,
  Swipe,
  SwipeItem
} from 'vant';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Loading)
  .use(Popup)
  .use(Field)
  .use(Toast)
  .use(Dialog)
  .use(Swipe)
  .use(SwipeItem);

export default {
  name: 'ActivityDetail',
  data() {
    return {
      activity: {},
      showRegisterPopup: false,
      submitting: false,
      activeTab: 'description', // 当前激活的标签页
      isTabsSticky: false, // 标签页是否吸顶
      scrollPosition: 0, // 当前滚动位置，用于调试
      scrollHandler: null, // 滚动事件处理器
      navBarHeight: 46, // 导航栏高度
      originalTabsTop: 0, // 标签页原始位置
      elementTop: 0, // 元素当前top位置，用于调试
      pollingTimer: null, // 定时检查器
      originalTabsTop: 0, // 标签页原始位置
      registerForm: {
        name: '',
        phone: '',
        cardType: '',
        idCard: '',
        gender: '',
        age: '',
        human: '',
        child: '',
        high: '',
        weight: '',
        educate: '',
        community: '',
        address: '',
        selfAdds: [] // 自增项目
      }
    };
  },
  computed: {
    shouldScroll() {
      return this.activity.title && this.activity.title.length > 15;
    },

    shouldShowButton() {
      // 当同时满足以下条件时才展示此按钮：
      // 1. 报名进行中
      // 2. 当前用户未报名
      if (!this.activity.id) return false;
      
      // 检查报名状态
      const isRegistrationOngoing = this.activity.registrationStatus === 'ongoing' || 
                                   this.activity.activityStatus === 'ongoing';
      
      // 检查用户是否已报名
      const isUserRegistered = this.activity.userRegistered === true;
      
      // 只有报名进行中且用户未报名时才显示按钮
      return isRegistrationOngoing && !isUserRegistered;
    },

    isButtonDisabled() {
      // 已报名时按钮可点击但置灰
      return false;
    }
  },
  mounted() {
    this.loadActivityDetail();
    // 延迟绑定滚动事件，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initStickyBehavior();
      }, 300);
    });
  },

  beforeDestroy() {
    this.cleanupStickyBehavior();
  },
  methods: {
    async loadActivityDetail() {
      try {
        const activityId = this.$route.params.id;

        // 1.5 活动详情接口调用
        const response = await this.$api.getActivityDetail(activityId);

        if (response && response.success === 1) {
          const data = response.value;

          // 根据接口文档映射数据
          this.activity = {
            id: activityId,
            title: data.actTitle, // 活动名称
            activityCode: `ACT${activityId}`,
            imageUrl: data.headerImg || require('@/images/img/background.png'), // 小封面
            images: data.images || [data.headerImg || require('@/images/img/background.png')], // 多张头图
            headerImgId: data.headerImgId, // 小封面Id
            activityStatus: data.activityStatus, // 活动状态
            activityTime: data.actTime, // 活动时间
            registrationTime: data.actTime, // 使用活动时间作为报名时间
            location: data.locationString, // 活动地址+详细地址
            notice: data.actNotice, // 活动须知
            description: data.actDesc, // 活动介绍
            participantCount: 0, // 接口未返回，设为默认值
            maxParticipants: 'unlimited', // 接口未返回，设为不限
            userRegistered: false, // 接口未返回，设为默认值
            registrationStatus: 'ongoing' // 默认为进行中
          };
        } else {
          // 如果接口调用失败，使用模拟数据
          const mockData = {
            '000009': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000009',
              activityStatus: 'not_started', // 未开始
              registrationStatus: 'not_started',
              participantCount: 0,
              maxParticipants: 10,
              userRegistered: false
            },
            '000007': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000007',
              activityStatus: 'ongoing', // 进行中
              registrationStatus: 'ongoing',
              participantCount: 0,
              maxParticipants: 'unlimited', // 不限
              userRegistered: false
            },
            '000006': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000006',
              activityStatus: 'ended', // 已结束
              registrationStatus: 'ended',
              participantCount: 0,
              maxParticipants: 10,
              userRegistered: true
            }
          };

          const data = mockData[activityId] || mockData['000009'];

          this.activity = {
            id: activityId,
            ...data,
            imageUrl: require('@/images/img/background.png'), // 使用本地图片
            images: [
              require('@/images/img/background.png'),
              'https://picsum.photos/seed/activity_1/800/400',
              'https://picsum.photos/seed/activity_2/800/400'
            ], // 模拟多张图片
            activityTime: 'yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm',
            registrationTime: 'yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm'
          };
        }
      } catch (error) {
        console.error('加载活动详情失败:', error);
        Toast('加载失败，请重试');
      }
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'not-started',
        'ongoing': 'ongoing',
        'ended': 'ended'
      };
      return statusMap[status] || 'not-started';
    },

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束'
      };
      return statusMap[status] || '未开始';
    },

    formatActivityTime(timeString) {
      if (!timeString) return '时间待定';
      
      try {
        // 处理时间字符串，支持多种格式
        let startTime, endTime;
        
        if (timeString.includes(' - ')) {
          [startTime, endTime] = timeString.split(' - ');
        } else if (timeString.includes(' 至 ')) {
          [startTime, endTime] = timeString.split(' 至 ');
        } else {
          return timeString; // 如果格式不匹配，直接返回原字符串
        }
        
        const formatDateTime = (dateTimeStr) => {
          const date = new Date(dateTimeStr.trim());
          if (isNaN(date.getTime())) return dateTimeStr;
          
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          
          const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
          const weekday = weekdays[date.getDay()];
          
          return `${year}.${month}.${day} 星期${weekday} ${hours}:${minutes}`;
        };
        
        const formattedStart = formatDateTime(startTime);
        const formattedEnd = formatDateTime(endTime);
        
        return `${formattedStart} 至 ${formattedEnd}`;
      } catch (error) {
        console.error('时间格式化失败:', error);
        return timeString;
      }
    },

    formatActivityAddress(addressString) {
      if (!addressString) return '地址待定';
      
      try {
        // 如果地址包含括号，说明已经有详细地址
        if (addressString.includes('（') && addressString.includes('）')) {
          return addressString;
        } else if (addressString.includes('(') && addressString.includes(')')) {
          return addressString;
        }
        
        // 如果没有括号，尝试解析地址
        // 这里可以根据实际业务逻辑来解析地址
        // 暂时直接返回原地址，支持换行
        return addressString.replace(/\n/g, '<br>');
      } catch (error) {
        console.error('地址格式化失败:', error);
        return addressString;
      }
    },

    formatActivityNotice(noticeString) {
      if (!noticeString) return '暂无须知';
      
      try {
        // 支持换行符识别，将\n转换为<br>
        return noticeString.replace(/\n/g, '<br>');
      } catch (error) {
        console.error('须知格式化失败:', error);
        return noticeString;
      }
    },

    switchTab(tabName) {
      this.activeTab = tabName;
    },

    getTabBackgroundImage() {
      if (this.activeTab === 'description') {
        return require('@/assets/images/detailContent.png');
      } else {
        return require('@/assets/images/detailContent2.png');
      }
    },

    // 获取吸顶头部样式
    getStickyHeaderStyle() {
      const baseStyle = {
        backgroundImage: `url(${this.getTabBackgroundImage()})`
      };

      // 如果是吸顶状态，动态设置top值
      if (this.isTabsSticky) {
        baseStyle.top = `${this.navBarHeight}px`;
      }

      return baseStyle;
    },


    getNoticeContent() {
      return `
        <h3>活动报名须知</h3>
        <p><strong>1. 报名条件</strong></p>
        <p>• 年龄要求：18-65周岁</p>
        <p>• 身体健康，无重大疾病史</p>
        <p>• 具备基本的活动参与能力</p>
        <p>• 遵守活动规则和纪律</p>
        
        <p><strong>2. 报名流程</strong></p>
        <p>• 填写完整的报名信息</p>
        <p>• 上传相关证件照片</p>
        <p>• 等待审核结果通知</p>
        <p>• 审核通过后确认参与</p>
        
        <p><strong>3. 注意事项</strong></p>
        <p>• 请提前15分钟到达活动现场</p>
        <p>• 携带有效身份证件</p>
        <p>• 穿着舒适的服装和鞋子</p>
        <p>• 如有特殊需求请提前告知</p>
        
        <p><strong>4. 取消政策</strong></p>
        <p>• 活动开始前24小时可免费取消</p>
        <p>• 24小时内取消将收取50%费用</p>
        <p>• 活动当天取消不予退款</p>
        <p>• 因不可抗力因素取消全额退款</p>
        
        <p><strong>5. 安全须知</strong></p>
        <p>• 活动期间请听从工作人员指挥</p>
        <p>• 注意个人财物安全</p>
        <p>• 如遇紧急情况请及时联系工作人员</p>
        <p>• 活动组织方已购买相关保险</p>
        
        <p><strong>6. 联系方式</strong></p>
        <p>• 咨询电话：400-123-4567</p>
        <p>• 微信客服：activity_service</p>
        <p>• 邮箱：<EMAIL></p>
        <p>• 工作时间：周一至周日 9:00-18:00</p>
        
        <p><strong>7. 其他说明</strong></p>
        <p>• 活动内容可能根据实际情况调整</p>
        <p>• 最终解释权归活动组织方所有</p>
        <p>• 参与即表示同意以上条款</p>
        <p>• 如有疑问请及时联系客服</p>
        
        <p style="color: #ff6b6b; font-weight: bold;">* 重要提醒：请仔细阅读以上须知，确保您了解并同意所有条款后再进行报名。</p>
      `;
    },

    // 初始化吸顶行为 - 单一可靠方案
    initStickyBehavior() {
      if (!this.$refs.tabsHeader) {
        console.warn('标签页头部元素未找到，延迟重试');
        setTimeout(() => this.initStickyBehavior(), 200);
        return;
      }

      console.log('初始化吸顶功能');

      // 获取导航栏高度
      this.navBarHeight = this.getNavBarHeight();
      console.log('导航栏高度:', this.navBarHeight);

      // 记录标签页原始位置
      this.originalTabsTop = this.getElementOffsetTop(this.$refs.tabsHeader);
      console.log('标签页原始位置:', this.originalTabsTop);

      // 只使用一个主要的监听方案，避免冲突
      this.setupMainScrollListener();
    },

    // 获取导航栏高度
    getNavBarHeight() {
      const navBar = document.querySelector('.van-nav-bar');
      if (navBar) {
        return navBar.offsetHeight;
      }
      // 如果没有找到导航栏，使用默认高度
      return 46; // Vant导航栏默认高度
    },

    // 获取元素相对于文档的偏移位置
    getElementOffsetTop(element) {
      let offsetTop = 0;
      while (element) {
        offsetTop += element.offsetTop;
        element = element.offsetParent;
      }
      return offsetTop;
    },

    // 设置滚动监听器 - 尝试多个滚动容器
    setupMainScrollListener() {
      this.scrollHandler = () => {
        console.log('滚动事件触发了！');
        this.checkStickyState();
      };

      // 尝试绑定到多个可能的滚动容器
      const scrollContainers = [
        window,
        document,
        document.documentElement,
        document.body,
        this.$el, // Vue组件根元素
        this.$el?.parentElement // 父元素
      ];

      console.log('开始绑定滚动监听器...');

      scrollContainers.forEach((container, index) => {
        if (container) {
          container.addEventListener('scroll', this.scrollHandler, { passive: true });
          console.log(`已绑定滚动监听器到容器 ${index}:`, container);
        }
      });

      // 也尝试监听 touchmove 事件（移动端）
      document.addEventListener('touchmove', this.scrollHandler, { passive: true });
      console.log('已绑定 touchmove 监听器');

      // 立即检查一次
      this.checkStickyState();

      // 添加定时器作为备用方案，每100ms检查一次
      this.pollingTimer = setInterval(() => {
        this.checkStickyState();
      }, 100);
      console.log('定时检查器已启动');
    },

    // 检查吸顶状态 - 使用最简单可靠的方法
    checkStickyState() {
      if (!this.$refs.tabsHeader) {
        console.log('tabsHeader 元素未找到');
        return;
      }

      const rect = this.$refs.tabsHeader.getBoundingClientRect();
      // 当标签页头部滚动到顶部时，开始吸顶
      const shouldBeSticky = rect.top <= 0;

      // 尝试多种方式获取滚动位置
      const scrollPositions = {
        windowPageYOffset: window.pageYOffset,
        documentElementScrollTop: document.documentElement.scrollTop,
        documentBodyScrollTop: document.body.scrollTop,
        windowScrollY: window.scrollY
      };

      // 更新调试信息
      this.scrollPosition = scrollPositions.windowPageYOffset || scrollPositions.documentElementScrollTop || scrollPositions.documentBodyScrollTop || scrollPositions.windowScrollY || 0;
      this.elementTop = rect.top;

      console.log('滚动检查详细信息:', {
        rectTop: rect.top,
        shouldBeSticky,
        currentSticky: this.isTabsSticky,
        scrollPositions,
        finalScrollPosition: this.scrollPosition,
        timestamp: new Date().getTime()
      });

      // 只有状态真正改变时才更新
      if (this.isTabsSticky !== shouldBeSticky) {
        console.log('🔄 切换吸顶状态:', shouldBeSticky);
        this.isTabsSticky = shouldBeSticky;
      }
    },

    // 手动切换吸顶状态（用于调试）
    toggleSticky() {
      this.isTabsSticky = !this.isTabsSticky;
      console.log('手动切换吸顶状态:', this.isTabsSticky);
    },

    // 强制检查状态（用于调试）
    forceCheck() {
      console.log('强制检查状态...');
      this.checkStickyState();
    },

    // 清理监听器
    cleanupStickyBehavior() {
      if (this.scrollHandler) {
        // 移除所有可能的滚动监听器
        const scrollContainers = [
          window,
          document,
          document.documentElement,
          document.body,
          this.$el,
          this.$el?.parentElement
        ];

        scrollContainers.forEach(container => {
          if (container) {
            container.removeEventListener('scroll', this.scrollHandler);
          }
        });

        document.removeEventListener('touchmove', this.scrollHandler);
        this.scrollHandler = null;
        console.log('所有滚动监听器已清理');
      }

      // 清理定时器
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
        console.log('定时检查器已清理');
      }
    },

    // 保留原有的handleScroll方法作为备用
    handleScroll(event) {
      // 更新滚动位置用于调试显示
      this.scrollPosition = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;

      if (!this.$refs.tabsHeader) return;

      const rect = this.$refs.tabsHeader.getBoundingClientRect();
      const shouldBeSticky = rect.top <= 0;
      this.updateStickyState(shouldBeSticky);
    },

    // 测试方法：手动切换吸顶状态
    testStickyToggle() {
      console.log('手动切换吸顶状态');
      this.isTabsSticky = !this.isTabsSticky;
    },

    // 测试方法：强制检查滚动位置
    forceCheckScroll() {
      console.log('强制检查滚动位置');
      this.handleScroll();
    },


    getButtonClass() {
      if (this.activity.userRegistered) {
        return 'registered';
      }
      return 'primary';
    },

    getButtonText() {
      if (this.activity.userRegistered) {
        return '已报名';
      }
      return '一键报名';
    },
    
    handleRegister() {
      // 检查报名状态
      if (this.activity.userRegistered) {
        // 已报名，跳转到报名详情页
        this.$router.push(`/registration-detail/${this.activity.id}`);
        return;
      }

      // 检查报名是否进行中
      const isRegistrationOngoing = this.activity.registrationStatus === 'ongoing' || 
                                   this.activity.activityStatus === 'ongoing';
      
      if (!isRegistrationOngoing) {
        Toast('当前活动报名未开始或已结束');
        return;
      }

      // 检查是否登录
      const isLoggedIn = this.checkLoginStatus();
      if (!isLoggedIn) {
        this.showLoginDialog();
        return;
      }

      // 显示报名弹窗
      this.showRegisterPopup = true;
    },

    checkLoginStatus() {
      // TODO: 检查用户登录状态
      return false; // 模拟未登录状态
    },

    showLoginDialog() {
      this.$dialog.confirm({
        title: '提示',
        message: '您尚未登录，请先登录',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        // 调用微信能力获取用户手机号
        this.getWechatPhone();
      }).catch(() => {
        // 用户取消
      });
    },

    getWechatPhone() {
      // TODO: 调用微信能力获取手机号
      // 获取成功后进入微信授权登录页，未获取到进入短信登录页
      console.log('获取微信手机号');
    },
    
    async submitRegister() {
      // 表单验证
      if (!this.registerForm.name.trim()) {
        Toast('请输入姓名');
        return;
      }

      if (!this.registerForm.phone.trim()) {
        Toast('请输入手机号');
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
        Toast('请输入正确的手机号');
        return;
      }

      this.submitting = true;

      try {
        // 1.9 一键报名接口调用
        const params = {
          id: this.activity.id, // 活动Id
          name: this.registerForm.name, // 姓名
          phone: this.registerForm.phone, // 手机号
          cardType: this.registerForm.cardType, // 证件类型
          idCard: this.registerForm.idCard, // 身份证号
          gender: this.registerForm.gender, // 性别
          age: this.registerForm.age, // 年龄
          human: this.registerForm.human ? parseInt(this.registerForm.human) : null, // 携带成人数
          child: this.registerForm.child ? parseInt(this.registerForm.child) : null, // 携带儿童数
          high: this.registerForm.high ? parseFloat(this.registerForm.high) : null, // 身高
          weight: this.registerForm.weight ? parseFloat(this.registerForm.weight) : null, // 体重
          educate: this.registerForm.educate, // 学历
          community: this.registerForm.community, // 社区
          address: this.registerForm.address, // 地址
          selfAdds: this.registerForm.selfAdds // 自增项目
        };

        const response = await this.$api.registerActivity(params);

        if (response && response.success === 1) {
          Toast.success('报名成功！');
          this.showRegisterPopup = false;
          this.activity.userRegistered = true;
          this.activity.participantCount++;

          // 重置表单
          this.registerForm = {
            name: '',
            phone: '',
            cardType: '',
            idCard: '',
            gender: '',
            age: '',
            human: '',
            child: '',
            high: '',
            weight: '',
            educate: '',
            community: '',
            address: '',
            selfAdds: []
          };
        } else {
          Toast(response?.respDesc || '报名失败，请重试');
        }
      } catch (error) {
        console.error('报名失败:', error);
        Toast('报名失败，请重试');
      } finally {
        this.submitting = false;
      }
    },
    
    handleContact(item) {
      if (item.type === 'phone') {
        window.location.href = `tel:${item.value}`;
      } else if (item.type === 'email') {
        window.location.href = `mailto:${item.value}`;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.activity-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.detail-content {
  background: white;
  margin: 0;
  border-radius: 0;
  overflow: hidden;
}


@keyframes scroll-text {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.activity-header {
  position: relative;
  
  .header-image-container {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }
  
  .header-swipe {
    height: 100%;
    
    :deep(.van-swipe__indicators) {
      bottom: 20px; // 指示器上移
    }
  }
  
  .header-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
  
  .activity-status-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    
    &.not-started {
      background: rgba(102, 102, 102, 0.8);
    }
    
    &.ongoing {
      background: rgba(82, 196, 26, 0.8);
    }
    
    &.ended {
      background: rgba(250, 140, 22, 0.8);
    }
  }
}


.activity-status-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .status-item {
    display: flex;
    align-items: center;

    .status-label {
      font-size: 14px;
      color: #666;
      margin-right: 8px;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;

      &.not-started {
        background: #f0f0f0;
        color: #666;
      }

      &.ongoing {
        background: #e8f5e8;
        color: #52c41a;
      }

      &.ended {
        background: #fff2e8;
        color: #fa8c16;
      }
    }
  }
}

.activity-info-card {
  width: 100%;
  min-height: 185px;
  margin: 0;
  border-radius: 16.5px;
  background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: -13px; // 向上移动，但留出空间给轮播指示器
  position: relative;
  z-index: 1;

  .activity-name {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600; /* Semibold */
    font-size: 18px;
    line-height: 25px;
    color: #292929;
    text-align: left;
    letter-spacing: 0;
    word-break: break-all;
    margin-bottom: 16px;
    flex-shrink: 0;
    padding: 20px 20px 0 20px;
  }

  .activity-time,
  .activity-location {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    flex-shrink: 0;
    padding: 0 20px;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 20px;
    }

    .info-icon {
      width: 12px;
      height: 12px;
      margin-right: 6px;
      flex-shrink: 0;
      object-fit: contain; // 防止图标变形
    }

    .info-text {
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-weight: 400; /* Regular */
      font-size: 11px;
      line-height: 15px;
      color: #AAAAAA;
      text-align: left;
      letter-spacing: 0;
      flex: 1;
    }
  }
}

// .participant-section {
//   padding: 16px;

//   .participant-text {
//     font-size: 14px;
//     color: #666;
//   }
// }

.activity-tabs-section {
  margin-top: -65px; // 再往上移一点
  
  .tabs-header {
    background-size: contain; // 改为contain确保贴图完全显示
    background-repeat: no-repeat;
    background-position: center center;
    border-bottom: none; // 去除下方的线
    transition: all 0.3s ease;
    position: relative;
    z-index: 2; // 层级比activity-location高
    min-height: 80px; // 增加高度确保贴图完全显示
    display: flex;
    align-items: center; // 垂直居中
    
    &.sticky {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1000 !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .tabs-container {
      display: flex;
      padding-left: 40px; // 左40pt
      padding-right: 45px; // 右35.5pt
      width: 100%;
      height: 100%;
      align-items: center; // 垂直居中
      justify-content: space-between; // 左右分布
      
      .tab-item {
        flex: none; // 不自动拉伸
        padding: 0;
        text-align: center; // 改为居中对齐
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 500; // Medium
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        display: flex;
        align-items: center; // 垂直居中
        justify-content: center; // 水平居中
        height: 100%;
        
        &.active {
          color: #292929; // 选中状态颜色
          
          &::after {
            display: none; // 去除下划线
          }
        }
        
        &:not(.active) {
          color: #FFFFFF; // 未选中状态颜色
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  
  .tabs-header-placeholder {
    height: 80px; // 与tabs-header相同的高度
    width: 100%;
  }
  
  .tabs-content {
    .tab-panel {
      padding: 20px 16px;
      border-bottom: 8px solid #f8f8f8;
      
      .description-content,
      .notice-content {
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        
        :deep(p) {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

.registration-info,
.contact-info {
  padding: 20px 16px;
  border-bottom: 8px solid #f8f8f8;
  
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  .registration-content,
  .contact-content {
    .info-item,
    .contact-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      
      .label {
        color: #666;
        min-width: 80px;
      }
      
      .value {
        color: #333;
        flex: 1;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .contact-item .value {
      color: #1989fa;
      cursor: pointer;
    }
  }
}

.registration-info,
.contact-info {
  .registration-content,
  .contact-content {
    .info-item,
    .contact-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      
      .label {
        color: #666;
        min-width: 80px;
      }
      
      .value {
        color: #333;
        flex: 1;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .contact-item .value {
      color: #1989fa;
      cursor: pointer;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 100;

  .register-button {
    border-radius: 24px;

    &.primary {
      background: linear-gradient(135deg, #d4af37, #b8941f);
      border: none;
      color: white;
    }

    &.registered {
      background: #f0f0f0;
      color: #999;
      border: 1px solid #ddd;
    }
  }
}

.register-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: bold;
    }
    
    .van-icon {
      font-size: 20px;
      color: #999;
    }
  }
  
  .register-form {
    flex: 1;
  }
  
  .popup-footer {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    
    .van-button {
      flex: 1;
    }
  }
}
</style>
