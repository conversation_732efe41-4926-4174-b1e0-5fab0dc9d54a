<template>
  <div class="test-sticky-page">
    <h1>滚动吸顶功能测试页面</h1>
    
    <!-- 调试信息 -->
    <div class="debug-info">
      <p>吸顶状态: {{ isTabsSticky ? '是' : '否' }}</p>
      <p>滚动位置: {{ scrollPosition }}px</p>
      <p>元素位置: {{ elementTop }}px</p>
      <button @click="testToggle">手动切换吸顶</button>
      <button @click="scrollToTop">滚动到顶部</button>
      <button @click="scrollToTabs">滚动到标签页</button>
    </div>

    <!-- 占位内容 -->
    <div class="placeholder-content">
      <h2>这里是一些占位内容</h2>
      <p>滚动页面查看吸顶效果</p>
      <div v-for="i in 10" :key="i" class="content-block">
        <h3>内容块 {{ i }}</h3>
        <p>这是第 {{ i }} 个内容块，用于测试滚动效果。</p>
      </div>
    </div>

    <!-- 测试标签页 -->
    <div class="tabs-section">
      <div 
        class="tabs-header" 
        :class="{ 'sticky': isTabsSticky }" 
        ref="tabsHeader"
      >
        <div class="tabs-container">
          <div class="tab-item active">测试标签1</div>
          <div class="tab-item">测试标签2</div>
        </div>
      </div>
      
      <!-- 占位元素 -->
      <div class="tabs-placeholder" v-show="isTabsSticky"></div>
      
      <!-- 标签页内容 -->
      <div class="tabs-content">
        <div v-for="i in 20" :key="i" class="content-item">
          <h4>标签页内容 {{ i }}</h4>
          <p>这是标签页下的内容，用于测试吸顶效果是否正常工作。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestSticky',
  data() {
    return {
      isTabsSticky: false,
      scrollPosition: 0,
      elementTop: 0,
      scrollHandler: null,
      navBarHeight: 0,
      originalTabsTop: 0
    };
  },
  
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initStickyBehavior();
      }, 300);
    });
  },
  
  beforeDestroy() {
    this.cleanupStickyBehavior();
  },
  
  methods: {
    initStickyBehavior() {
      if (!this.$refs.tabsHeader) {
        setTimeout(() => this.initStickyBehavior(), 200);
        return;
      }

      console.log('初始化吸顶功能');

      // 获取导航栏高度（如果有的话）
      this.navBarHeight = this.getNavBarHeight();

      // 记录标签页原始位置
      this.originalTabsTop = this.getElementOffsetTop(this.$refs.tabsHeader);

      // 使用单一监听方案
      this.setupMainScrollListener();
    },

    getNavBarHeight() {
      const navBar = document.querySelector('.van-nav-bar');
      return navBar ? navBar.offsetHeight : 0;
    },

    getElementOffsetTop(element) {
      let offsetTop = 0;
      while (element) {
        offsetTop += element.offsetTop;
        element = element.offsetParent;
      }
      return offsetTop;
    },

    setupMainScrollListener() {
      let ticking = false;

      this.scrollHandler = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            this.checkStickyState();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', this.scrollHandler, { passive: true });
      this.checkStickyState();
    },

    checkStickyState() {
      if (!this.$refs.tabsHeader) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || 0;
      const triggerPoint = this.originalTabsTop - this.navBarHeight;

      // 添加缓冲区避免抖动
      const buffer = 2;
      let shouldBeSticky;

      if (this.isTabsSticky) {
        shouldBeSticky = scrollTop > (triggerPoint - buffer);
      } else {
        shouldBeSticky = scrollTop > (triggerPoint + buffer);
      }

      this.scrollPosition = scrollTop;
      this.elementTop = this.$refs.tabsHeader.getBoundingClientRect().top;

      if (this.isTabsSticky !== shouldBeSticky) {
        console.log('切换吸顶状态:', shouldBeSticky);
        this.isTabsSticky = shouldBeSticky;
      }
    },



    cleanupStickyBehavior() {
      if (this.scrollHandler) {
        window.removeEventListener('scroll', this.scrollHandler);
        this.scrollHandler = null;
      }
    },



    testToggle() {
      this.isTabsSticky = !this.isTabsSticky;
    },

    scrollToTop() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    },

    scrollToTabs() {
      if (this.$refs.tabsHeader) {
        this.$refs.tabsHeader.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.test-sticky-page {
  min-height: 200vh;
  padding: 20px;
}

.debug-info {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  z-index: 9999;
  font-size: 12px;
  
  button {
    margin: 5px 0;
    padding: 5px 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    width: 100%;
  }
}

.placeholder-content {
  margin-bottom: 50px;
  
  .content-block {
    margin: 20px 0;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
  }
}

.tabs-section {
  .tabs-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 0;
    transition: all 0.3s ease;
    
    &.sticky {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1000 !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .tabs-container {
      display: flex;
      justify-content: center;
      gap: 40px;
      
      .tab-item {
        padding: 10px 20px;
        cursor: pointer;
        border-radius: 20px;
        transition: all 0.3s ease;
        
        &.active {
          background: rgba(255, 255, 255, 0.2);
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
  
  .tabs-placeholder {
    height: 70px;
  }
  
  .tabs-content {
    .content-item {
      margin: 20px 0;
      padding: 20px;
      background: white;
      border: 1px solid #eee;
      border-radius: 8px;
    }
  }
}
</style>
