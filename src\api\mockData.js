// Mock数据文件
// 用于模拟API接口响应

// 活动状态枚举
const ACTIVITY_STATUS = {
  NOT_STARTED: 'not_started', // 未开始
  ONGOING: 'ongoing', // 进行中
  ENDED: 'ended' // 已结束
};

// 生成模拟活动数据
function generateMockActivity(id, title = '', status = ACTIVITY_STATUS.ONGOING) {
  return {
    id: id.toString(),
    actTitle: title || `活动名称${id}活动名称活动名称活动名称活动名称活动名称活动名称`,
    headerImg: `https://picsum.photos/seed/activity_${id}/800/400`,
    headerImgId: Math.floor(Math.random() * 1000),
    activityStatus: status,
    actTime: '2025-01-01 09:00 - 2025-01-02 17:00',
    registerTime: '2024-12-15 00:00 - 2024-12-31 23:59',
    numRage: Math.floor(Math.random() * 100) + 10,
    registrantsNum: Math.floor(Math.random() * 50)
  };
}

// 模拟活动列表数据
export const mockActivityList = [
  generateMockActivity(1, '冬季健康讲座', ACTIVITY_STATUS.ONGOING),
  generateMockActivity(2, '儿童疫苗接种日', ACTIVITY_STATUS.NOT_STARTED),
  generateMockActivity(3, '糖尿病防治知识讲座', ACTIVITY_STATUS.ENDED),
  generateMockActivity(4, '高血压患者自我管理工作坊', ACTIVITY_STATUS.ONGOING),
  generateMockActivity(5, '孕妇产前保健讲座', ACTIVITY_STATUS.ONGOING),
  generateMockActivity(6, '青少年视力保护讲座', ACTIVITY_STATUS.NOT_STARTED),
  generateMockActivity(7, '老年人跌倒预防训练', ACTIVITY_STATUS.ENDED),
  generateMockActivity(8, '心理健康咨询日', ACTIVITY_STATUS.ONGOING),
  generateMockActivity(9, '中医养生保健讲座', ACTIVITY_STATUS.ONGOING),
  generateMockActivity(10, '急救技能培训课程', ACTIVITY_STATUS.NOT_STARTED)
];

// 模拟活动详情数据
export const mockActivityDetail = {
  actTitle: '这里是活动名称可以有多行,多行就换行展示',
  headerImg: 'https://picsum.photos/seed/activity_detail/800/400',
  images: [
    'https://picsum.photos/seed/activity_detail_1/800/400',
    'https://picsum.photos/seed/activity_detail_2/800/400',
    'https://picsum.photos/seed/activity_detail_3/800/400'
  ],
  headerImgId: 123,
  activityStatus: ACTIVITY_STATUS.ONGOING,
  actTime: '2024.05.01 09:30 - 2024.05.30 18:30',
  locationString: '分行二楼城市会客厅(凤起路432号)',
  actNotice: '这里是活动须知,支持换行\n1.注意天气\n2.如有随行人员,请携带随行人员的有效证件信息\n3.吧啦吧啦\n*本次活动平台具有最终解释权',
  actDesc: '本次讲座将邀请著名专家讲解冬季常见疾病的预防和治疗方法，包括感冒、流感、肺炎等呼吸系统疾病的防治知识，以及心脑血管疾病的冬季保健要点。讲座内容丰富实用，欢迎广大市民朋友参加。'
};

// 模拟活动搜索结果
export const mockSearchResults = [
  {
    actTitle: '冬季健康讲座',
    headerImg: 'https://picsum.photos/seed/search_1/800/400',
    headerImgId: 123,
    activityStatus: ACTIVITY_STATUS.ONGOING,
    actTime: '2025-01-01 09:00 - 2025-01-02 17:00',
    register: 0, // 未报名
    numRage: 100,
    registerCount: 50
  },
  {
    actTitle: '中医养生保健讲座',
    headerImg: 'https://picsum.photos/seed/search_2/800/400',
    headerImgId: 124,
    activityStatus: ACTIVITY_STATUS.ONGOING,
    actTime: '2024-12-25 14:00 - 16:00',
    register: 1, // 已报名
    numRage: 80,
    registerCount: 65
  }
];

// 模拟API延迟
function mockDelay() {
  return new Promise(resolve => {
    setTimeout(resolve, 300 + Math.random() * 700); // 300-1000ms随机延迟
  });
}

// 模拟Banner列表数据
export const mockBannerList = [
  {
    id: '1',
    title: '冬季健康讲座',
    imageUrl: 'https://picsum.photos/seed/banner_1/800/320',
    linkType: 'activity',
    linkId: '1',
    linkUrl: ''
  },
  {
    id: '2',
    title: '新年健康礼包',
    imageUrl: 'https://picsum.photos/seed/banner_2/800/320',
    linkType: 'h5',
    linkId: '',
    linkUrl: 'https://example.com/promotion'
  },
  {
    id: '3',
    title: '专家义诊活动',
    imageUrl: 'https://picsum.photos/seed/banner_3/800/320',
    linkType: 'activity',
    linkId: '4',
    linkUrl: ''
  }
];

// 模拟精选活动列表数据
export const mockFeaturedList = [
  {
    id: '101',
    title: '专家面对面咨询活动',
    subtitle: '三甲名医坐诊，限时名额',
    imageUrl: 'https://picsum.photos/seed/featured_1/600/360',
    linkType: 'activity',
    linkId: '101',
    linkUrl: ''
  },
  {
    id: '102',
    title: '健康体检优惠套餐',
    subtitle: '年度体检早预约早安心',
    imageUrl: 'https://picsum.photos/seed/featured_2/600/360',
    linkType: 'activity',
    linkId: '102',
    linkUrl: ''
  },
  {
    id: '103',
    title: '慢病管理会员计划',
    subtitle: '血压血糖全程管理',
    imageUrl: 'https://picsum.photos/seed/featured_3/600/360',
    linkType: 'h5',
    linkId: '',
    linkUrl: 'https://example.com/plan'
  },
  {
    id: '104',
    title: '母婴保健课堂',
    subtitle: '新手父母必修课',
    imageUrl: 'https://picsum.photos/seed/featured_4/600/360',
    linkType: 'activity',
    linkId: '104',
    linkUrl: ''
  }
];

// 模拟活动类型列表数据
export const mockCategoryList = [
  {
    id: '1',
    name: '健康讲座',
    icon: 'https://picsum.photos/seed/cate_1/48/48'
  },
  {
    id: '2',
    name: '义诊活动',
    icon: 'https://picsum.photos/seed/cate_2/48/48'
  },
  {
    id: '3',
    name: '体检优惠',
    icon: 'https://picsum.photos/seed/cate_3/48/48'
  },
  {
    id: '4',
    name: '慢性病管理',
    icon: 'https://picsum.photos/seed/cate_4/48/48'
  },
  {
    id: '5',
    name: '妇幼保健',
    icon: 'https://picsum.photos/seed/cate_5/48/48'
  }
];

// 模拟获取活动列表
export const mockGetActivityList = async (params = {}) => {
  await mockDelay();

  console.log('mockGetActivityList 被调用，参数:', params);

  // 确保始终返回数据
  let filteredList = [...mockActivityList];

  // 如果没有数据，创建一些默认数据
  if (filteredList.length === 0) {
    filteredList = [
      generateMockActivity(1, '冬季健康讲座', ACTIVITY_STATUS.ONGOING),
      generateMockActivity(2, '儿童疫苗接种日', ACTIVITY_STATUS.NOT_STARTED),
      generateMockActivity(3, '糖尿病防治知识讲座', ACTIVITY_STATUS.ENDED)
    ];
  }

  // 按类型筛选 - 简化逻辑确保始终有数据返回
  if (params.typeId && params.typeId !== 'all' && params.typeId !== null) {
    // 这里简单模拟，实际项目中应该根据真实类型ID筛选
    const tempFiltered = filteredList.filter((_, index) => index % 2 === 0);
    // 如果筛选后没有数据，使用原始列表
    filteredList = tempFiltered.length > 0 ? tempFiltered : filteredList;
  }

  // 排序逻辑
  if (params.popularity === 1) {
    // 人气排行 - 按参与人数排序
    filteredList.sort((a, b) => {
      if (params.selectSort === 1) {
        // 升序
        return a.registrantsNum - b.registrantsNum;
      } else {
        // 降序
        return b.registrantsNum - a.registrantsNum;
      }
    });
  } else {
    // 最近发布 - 按ID降序排序（模拟最新创建的活动）
    filteredList.sort((a, b) => b.id - a.id);
  }

  // 分页
  const page = Number(params.page || 1);
  const pageSize = Number(params.pageSize || 10);
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const pageList = filteredList.slice(start, end);

  console.log('mockGetActivityList 返回数据:', {
    success: 1,
    value: pageList,
    total: filteredList.length,
    page,
    pageSize
  });

  return {
    success: 1,
    value: pageList,
    total: filteredList.length,
    page,
    pageSize
  };
};

// 模拟获取活动详情
export const mockGetActivityDetail = async (actId) => {
  await mockDelay();
  
  // 查找对应ID的活动
  const activity = mockActivityList.find(item => item.id === actId.toString());
  
  if (activity) {
    return {
      success: 1,
      value: {
        ...mockActivityDetail,
        actTitle: activity.actTitle,
        activityStatus: activity.activityStatus,
        actTime: activity.actTime
      }
    };
  } else {
    return {
      success: 1,
      value: mockActivityDetail
    };
  }
};

// 模拟搜索活动
export const mockSearchActivity = async (params = {}) => {
  await mockDelay();
  
  // 模拟搜索逻辑
  let results = [...mockSearchResults];
  
  if (params.actTitle) {
    const keyword = params.actTitle.toLowerCase();
    results = results.filter(item => 
      item.actTitle.toLowerCase().includes(keyword)
    );
  }
  
  return {
    success: 1,
    value: results
  };
};

// 模拟筛选活动
export const mockChooseActivity = async (params = {}) => {
  await mockDelay();

  console.log('mockChooseActivity 被调用，参数:', params);

  // 确保始终返回数据
  let filteredList = [...mockActivityList];

  // 如果没有数据，创建一些默认数据
  if (filteredList.length === 0) {
    filteredList = [
      generateMockActivity(1, '冬季健康讲座', ACTIVITY_STATUS.ONGOING),
      generateMockActivity(2, '儿童疫苗接种日', ACTIVITY_STATUS.NOT_STARTED),
      generateMockActivity(3, '糖尿病防治知识讲座', ACTIVITY_STATUS.ENDED)
    ];
  }

  // 类型筛选 - 确保有数据返回
  if (params.typeId && params.typeId !== 'all' && params.typeId !== null) {
    const tempFiltered = filteredList.filter((_, index) => index % 2 === 0);
    filteredList = tempFiltered.length > 0 ? tempFiltered : filteredList;
  }
  
  if (params.activityStatus && params.activityStatus !== 'all' && params.activityStatus !== null) {
    filteredList = filteredList.filter(
      item => item.activityStatus === params.activityStatus
    );
  }
  
  if (params.registerStatus && params.registerStatus !== 'all' && params.registerStatus !== null) {
    // 根据index.vue中传递的注册状态值进行筛选
    if (params.registerStatus === 'ongoing') {
      // 报名进行中 - 排除已结束的活动
      filteredList = filteredList.filter(
        item => item.activityStatus !== ACTIVITY_STATUS.ENDED
      );
    } else if (params.registerStatus === 'ended') {
      // 报名已结束 - 只显示已结束的活动
      filteredList = filteredList.filter(
        item => item.activityStatus === ACTIVITY_STATUS.ENDED
      );
    } else if (params.registerStatus === 'not_started') {
      // 报名未开始 - 只显示未开始的活动
      filteredList = filteredList.filter(
        item => item.activityStatus === ACTIVITY_STATUS.NOT_STARTED
      );
    }
  }
  
  // 排序逻辑 - 从params中获取排序参数
  // 如果是从activity.js中调用，需要传递排序参数
  const { popularity = 0, selectSort = 0 } = params;
  
  if (popularity === 1) {
    // 人气排行 - 按参与人数排序
    filteredList.sort((a, b) => {
      if (selectSort === 1) {
        // 升序
        return a.registrantsNum - b.registrantsNum;
      } else {
        // 降序
        return b.registrantsNum - a.registrantsNum;
      }
    });
  } else {
    // 最近发布 - 按ID降序排序（模拟最新创建的活动）
    filteredList.sort((a, b) => b.id - a.id);
  }
  
  // 分页
  const page = Number(params.page || 1);
  const pageSize = Number(params.pageSize || 10);
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const pageList = filteredList.slice(start, end);

  console.log('mockChooseActivity 返回数据:', {
    success: 1,
    value: pageList,
    total: filteredList.length,
    page,
    pageSize
  });

  return {
    success: 1,
    value: pageList,
    total: filteredList.length,
    page,
    pageSize
  };
};

// 模拟报名活动
export const mockRegisterActivity = async (data) => {
  await mockDelay();
  
  // 模拟报名成功
  if (data && data.id && data.name && data.phone) {
    return {
      success: 1,
      value: { success: true },
      msg: '报名成功'
    };
  } else {
    return {
      success: 0,
      msg: '报名信息不完整'
    };
  }
};

// 模拟获取Banner列表
export const mockGetBannerList = async () => {
  await mockDelay();
  return {
    success: 1,
    value: mockBannerList
  };
};

// 模拟获取精选活动列表
export const mockGetFeaturedActivityList = async () => {
  await mockDelay();
  return {
    success: 1,
    value: mockFeaturedList
  };
};

// 模拟获取活动类型列表
export const mockGetActivityCategoryList = async () => {
  await mockDelay();
  return {
    success: 1,
    value: mockCategoryList
  };
};

// 导出所有mock函数
export default {
  getActivityList: mockGetActivityList,
  getActivityDetail: mockGetActivityDetail,
  searchActivity: mockSearchActivity,
  chooseActivity: mockChooseActivity,
  registerActivity: mockRegisterActivity,
  getBannerList: mockGetBannerList,
  getFeaturedActivityList: mockGetFeaturedActivityList,
  getActivityCategoryList: mockGetActivityCategoryList
};